import { supabase } from './supabase'
import type { User } from '@/types'

export class SupabaseAuthService {
  static async signUp(email: string, password: string, name?: string) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name: name || email.split('@')[0]
        }
      }
    })
    
    if (error) throw error
    return data
  }
  
  static async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    
    if (error) throw error
    return data
  }
  
  static async signOut() {
    const { error } = await supabase.auth.signOut()
    if (error) throw error
  }
  
  static async getCurrentUser(): Promise<User | null> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) return null
    
    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()
    
    return {
      id: user.id,
      email: user.email!,
      name: profile?.name || user.user_metadata?.name || user.email!.split('@')[0],
      createdAt: new Date(user.created_at),
      updatedAt: new Date(profile?.updated_at || user.updated_at)
    }
  }
}