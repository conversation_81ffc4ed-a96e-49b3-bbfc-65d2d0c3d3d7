import { NextRequest, NextResponse } from 'next/server'
import { BlogPostModel } from '@/lib/database-supabase'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const featured = searchParams.get('featured')
    const limit = searchParams.get('limit')
    
    let posts
    if (featured === 'true') {
      posts = await BlogPostModel.getFeatured(limit ? parseInt(limit) : undefined)
    } else {
      posts = await BlogPostModel.getAll()
    }

    return NextResponse.json({
      success: true,
      data: posts
    })
  } catch (error: any) {
    console.error('Get blog posts API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to fetch blog posts' 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const postData = await request.json()
    
    const post = await BlogPostModel.create(postData)

    return NextResponse.json({
      success: true,
      data: post
    }, { status: 201 })
  } catch (error: any) {
    console.error('Create blog post API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to create blog post' 
      },
      { status: 500 }
    )
  }
}
