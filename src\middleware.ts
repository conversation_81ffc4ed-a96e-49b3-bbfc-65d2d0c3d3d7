import { createServerClient } from '@supabase/ssr'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  console.log('🔍 Middleware: Processing request for:', req.nextUrl.pathname)

  let res = NextResponse.next({
    request: {
      headers: req.headers,
    },
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return req.cookies.get(name)?.value
        },
        set(name: string, value: string, options: any) {
          req.cookies.set({
            name,
            value,
            ...options,
          })
          res = NextResponse.next({
            request: {
              headers: req.headers,
            },
          })
          res.cookies.set({
            name,
            value,
            ...options,
          })
        },
        remove(name: string, options: any) {
          req.cookies.set({
            name,
            value: '',
            ...options,
          })
          res = NextResponse.next({
            request: {
              headers: req.headers,
            },
          })
          res.cookies.set({
            name,
            value: '',
            ...options,
          })
        },
      },
    }
  )

  // Refresh session if expired - required for Server Components
  const {
    data: { session },
  } = await supabase.auth.getSession()

  console.log('🔍 Middleware: Session:', session ? 'authenticated' : 'not authenticated')
  console.log('🔍 Middleware: Session details:', session ? { user_id: session.user?.id, expires_at: session.expires_at } : 'null')

  // Protected routes that require authentication
  const protectedRoutes = ['/library', '/watch']
  const isProtectedRoute = protectedRoutes.some(route =>
    req.nextUrl.pathname.startsWith(route)
  )

  console.log('🔍 Middleware: Is protected route:', isProtectedRoute)

  // Temporarily disable middleware protection to debug routing issues
  // TODO: Re-enable after fixing session sync
  /*
  // If accessing a protected route without authentication, redirect to login
  if (isProtectedRoute && !session) {
    console.log('❌ Middleware: Redirecting to login - no session for protected route')
    const redirectUrl = new URL('/auth/login', req.url)
    redirectUrl.searchParams.set('redirectTo', req.nextUrl.pathname)
    return NextResponse.redirect(redirectUrl)
  }
  */

  // If authenticated user tries to access auth pages, redirect to library
  if (session && req.nextUrl.pathname.startsWith('/auth/')) {
    console.log('✅ Middleware: Redirecting authenticated user from auth page to library')
    return NextResponse.redirect(new URL('/library', req.url))
  }

  console.log('✅ Middleware: Allowing request to proceed')

  return res
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
