import { createClient } from '@supabase/supabase-js'
import * as dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY! // Service role key for admin operations

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables:')
  console.error('NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✓' : '✗')
  console.error('SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? '✓' : '✗')
  console.error('\nPlease add SUPABASE_SERVICE_ROLE_KEY to your .env.local file')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Sample data
const sampleTutorials = [
  {
    id: '1',
    title: 'The Art of Card Control',
    description: 'Master the fundamental techniques of card control that form the foundation of all advanced card magic. Learn the classic methods used by professionals worldwide.',
    price: 49.99,
    featured: true,
    difficulty: 'intermediate',
    duration: 3600, // 1 hour in seconds
    cover_image: '/images/tutorials/card-control.jpg',
    preview_video_url: '/videos/previews/card-control-preview.mp4'
  },
  {
    id: '2',
    title: 'Advanced False Shuffles',
    description: 'Discover the secrets of maintaining complete control while appearing to shuffle the deck thoroughly. Essential techniques for the serious card magician.',
    price: 39.99,
    featured: true,
    difficulty: 'advanced',
    duration: 2700, // 45 minutes
    cover_image: '/images/tutorials/false-shuffles.jpg',
    preview_video_url: '/videos/previews/false-shuffles-preview.mp4'
  },
  {
    id: '3',
    title: 'Elegant Card Forces',
    description: 'Learn sophisticated forcing techniques that appear completely natural. These methods will elevate your card magic to a professional level.',
    price: 34.99,
    featured: false,
    difficulty: 'intermediate',
    duration: 2400, // 40 minutes
    cover_image: '/images/tutorials/card-forces.jpg',
    preview_video_url: '/videos/previews/card-forces-preview.mp4'
  }
]

const sampleVideos = [
  // Videos for Tutorial 1
  {
    id: '1',
    tutorial_id: '1',
    title: 'Introduction to Card Control',
    description: 'Overview of card control principles and their importance in card magic.',
    duration: 600, // 10 minutes
    order_index: 1,
    video_url: '/videos/tutorials/1/intro-card-control.mp4'
  },
  {
    id: '2',
    tutorial_id: '1',
    title: 'The Hindu Shuffle Control',
    description: 'Master the Hindu shuffle while maintaining control of selected cards.',
    duration: 900, // 15 minutes
    order_index: 2,
    video_url: '/videos/tutorials/1/hindu-shuffle-control.mp4'
  },
  {
    id: '3',
    tutorial_id: '1',
    title: 'Overhand Shuffle Techniques',
    description: 'Advanced overhand shuffle controls for maintaining card positions.',
    duration: 1200, // 20 minutes
    order_index: 3,
    video_url: '/videos/tutorials/1/overhand-shuffle.mp4'
  },
  // Videos for Tutorial 2
  {
    id: '4',
    tutorial_id: '2',
    title: 'The Perfect Riffle Shuffle',
    description: 'Execute a perfect riffle shuffle that maintains the entire deck order.',
    duration: 800, // 13 minutes
    order_index: 1,
    video_url: '/videos/tutorials/2/perfect-riffle.mp4'
  },
  {
    id: '5',
    tutorial_id: '2',
    title: 'Zarrow Shuffle Mastery',
    description: 'The ultimate false shuffle that fools even experienced magicians.',
    duration: 1000, // 16 minutes
    order_index: 2,
    video_url: '/videos/tutorials/2/zarrow-shuffle.mp4'
  }
]

const sampleBlogPosts = [
  {
    id: '1',
    title: 'The Philosophy of Magic Performance',
    content: 'Magic is not just about tricks and techniques—it\'s about creating moments of wonder and connecting with your audience on a deeper level...',
    excerpt: 'Exploring the deeper meaning behind magical performance and how to create truly memorable experiences.',
    featured: true,
    published: true,
    author: 'Magic Academy',
    tags: ['philosophy', 'performance', 'audience']
  },
  {
    id: '2',
    title: 'Building Your Magic Library',
    content: 'Every serious magician needs a well-curated library of magical knowledge. Here\'s how to build yours effectively...',
    excerpt: 'Essential books, videos, and resources every magician should have in their collection.',
    featured: false,
    published: true,
    author: 'Magic Academy',
    tags: ['education', 'resources', 'learning']
  }
]

const samplePortfolioItems = [
  {
    id: '1',
    title: 'Corporate Event - Tech Conference 2024',
    description: 'Close-up magic performance for 500+ attendees at a major technology conference.',
    image_url: '/images/portfolio/tech-conference.jpg',
    category: 'corporate',
    featured: true,
    date: '2024-03-15'
  },
  {
    id: '2',
    title: 'Wedding Reception Magic',
    description: 'Intimate close-up performance creating magical moments for wedding guests.',
    image_url: '/images/portfolio/wedding-magic.jpg',
    category: 'wedding',
    featured: true,
    date: '2024-02-20'
  }
]

async function createTestUser() {
  try {
    console.log('Creating test user...')

    // First check if user already exists
    const { data: existingUsers } = await supabase.auth.admin.listUsers()
    const existingUser = existingUsers?.users?.find(user => user.email === '<EMAIL>')

    if (existingUser) {
      console.log('✓ Test user already exists:', existingUser.email)
      return existingUser
    }

    // Create user with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'password123',
      email_confirm: true, // Skip email confirmation
      user_metadata: {
        name: 'Demo User'
      }
    })

    if (authError) {
      console.error('Error creating auth user:', authError)
      return null
    }

    console.log('✓ Test user created successfully:', authData.user?.email)
    return authData.user
  } catch (error) {
    console.error('Error in createTestUser:', error)
    return null
  }
}

async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...')

    // Clear existing data (be careful in production!)
    console.log('🧹 Clearing existing data...')
    await supabase.from('videos').delete().neq('id', '0')
    await supabase.from('purchases').delete().neq('id', '0')
    await supabase.from('tutorials').delete().neq('id', '0')
    await supabase.from('blog_posts').delete().neq('id', '0')
    await supabase.from('portfolio').delete().neq('id', '0')

    // Create test user first
    const testUser = await createTestUser()

    // Insert tutorials
    console.log('📚 Inserting tutorials...')
    const { error: tutorialsError } = await supabase
      .from('tutorials')
      .insert(sampleTutorials)

    if (tutorialsError) {
      console.error('❌ Error inserting tutorials:', tutorialsError)
      return
    }
    console.log('✓ Tutorials inserted successfully')

    // Insert videos
    console.log('🎥 Inserting videos...')
    const { error: videosError } = await supabase
      .from('videos')
      .insert(sampleVideos)

    if (videosError) {
      console.error('❌ Error inserting videos:', videosError)
      return
    }
    console.log('✓ Videos inserted successfully')

    // Insert blog posts
    console.log('📝 Inserting blog posts...')
    const { error: blogError } = await supabase
      .from('blog_posts')
      .insert(sampleBlogPosts)

    if (blogError) {
      console.error('❌ Error inserting blog posts:', blogError)
      return
    }
    console.log('✓ Blog posts inserted successfully')

    // Insert portfolio items
    console.log('🎨 Inserting portfolio items...')
    const { error: portfolioError } = await supabase
      .from('portfolio')
      .insert(samplePortfolioItems)

    if (portfolioError) {
      console.error('❌ Error inserting portfolio items:', portfolioError)
      return
    }
    console.log('✓ Portfolio items inserted successfully')

    // Create a sample purchase for the test user
    if (testUser) {
      console.log('🛒 Creating sample purchase for test user...')
      const { error: purchaseError } = await supabase
        .from('purchases')
        .insert([{
          id: 'purchase-1',
          user_id: testUser.id,
          tutorial_id: '1', // The Art of Card Control
          amount: 49.99,
          paypal_order_id: 'DEMO-ORDER-123',
          status: 'completed'
        }])

      if (purchaseError) {
        console.error('❌ Error creating sample purchase:', purchaseError)
      } else {
        console.log('✓ Sample purchase created successfully')
      }
    }

    console.log('\n🎉 Database seeding completed successfully!')
    console.log('\n📋 Test User Credentials:')
    console.log('Email: <EMAIL>')
    console.log('Password: password123')
    console.log('\n🚀 You can now log in to the application!')
  } catch (error) {
    console.error('❌ Error seeding database:', error)
  }
}

// Run the seeding function
seedDatabase()
