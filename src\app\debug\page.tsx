'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useEffect, useState } from 'react'

export default function DebugPage() {
  const { user, loading } = useAuth()
  const [debugInfo, setDebugInfo] = useState<any>({})

  useEffect(() => {
    async function checkSupabaseConnection() {
      try {
        console.log('🔍 Debug: Checking Supabase connection...')
        
        // Test database connection
        const response = await fetch('/api/debug')
        const data = await response.json()
        
        setDebugInfo({
          user,
          loading,
          apiResponse: data,
          timestamp: new Date().toISOString()
        })
        
        console.log('🔍 Debug: API response:', data)
      } catch (error) {
        console.error('❌ Debug: Error:', error)
        setDebugInfo({
          user,
          loading,
          error: error.message,
          timestamp: new Date().toISOString()
        })
      }
    }

    if (!loading) {
      checkSupabaseConnection()
    }
  }, [user, loading])

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Debug Information</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Authentication Status</h2>
          <div className="space-y-2">
            <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
            <p><strong>User:</strong> {user ? user.email : 'Not authenticated'}</p>
            <p><strong>User ID:</strong> {user?.id || 'N/A'}</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Debug Information</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>

        <div className="mt-6 space-x-4">
          <button 
            onClick={() => window.location.reload()} 
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Refresh
          </button>
          <a 
            href="/auth/login" 
            className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 inline-block"
          >
            Go to Login
          </a>
          <a 
            href="/library" 
            className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 inline-block"
          >
            Go to Library
          </a>
        </div>
      </div>
    </div>
  )
}
