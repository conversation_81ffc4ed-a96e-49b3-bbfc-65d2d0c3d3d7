import { NextRequest, NextResponse } from 'next/server'
import { TutorialModel } from '@/lib/database-supabase'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const featured = searchParams.get('featured')
    
    let tutorials
    if (featured === 'true') {
      tutorials = await TutorialModel.getFeatured()
    } else {
      tutorials = await TutorialModel.getAll()
    }

    return NextResponse.json({
      success: true,
      data: tutorials
    })
  } catch (error: any) {
    console.error('Get tutorials API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to fetch tutorials' 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const tutorialData = await request.json()
    
    const tutorial = await TutorialModel.create(tutorialData)

    return NextResponse.json({
      success: true,
      data: tutorial
    }, { status: 201 })
  } catch (error: any) {
    console.error('Create tutorial API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to create tutorial' 
      },
      { status: 500 }
    )
  }
}
