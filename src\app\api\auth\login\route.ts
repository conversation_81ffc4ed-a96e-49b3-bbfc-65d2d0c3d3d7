import { NextRequest, NextResponse } from 'next/server'
import { SupabaseAuthService } from '@/lib/auth-supabase'

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json()

    if (!email || !password) {
      return NextResponse.json(
        { success: false, error: 'Email and password are required' },
        { status: 400 }
      )
    }

    const { user, session } = await SupabaseAuthService.signIn(email, password)

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name
        },
        session: {
          access_token: session.access_token,
          refresh_token: session.refresh_token
        }
      }
    })
  } catch (error: any) {
    console.error('Login API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Login failed' 
      },
      { status: 401 }
    )
  }
}
