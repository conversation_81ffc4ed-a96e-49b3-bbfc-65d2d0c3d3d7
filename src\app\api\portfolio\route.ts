import { NextRequest, NextResponse } from 'next/server'
import { PortfolioModel } from '@/lib/database-supabase'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const featured = searchParams.get('featured')
    
    let items
    if (featured === 'true') {
      items = await PortfolioModel.getFeatured()
    } else {
      items = await PortfolioModel.getAll()
    }

    return NextResponse.json({
      success: true,
      data: items
    })
  } catch (error: any) {
    console.error('Get portfolio items API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to fetch portfolio items' 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const itemData = await request.json()
    
    const item = await PortfolioModel.create(itemData)

    return NextResponse.json({
      success: true,
      data: item
    }, { status: 201 })
  } catch (error: any) {
    console.error('Create portfolio item API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to create portfolio item' 
      },
      { status: 500 }
    )
  }
}
