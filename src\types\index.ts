// User types
export interface User {
  id: string
  email: string
  name: string
  created_at?: string
  updated_at?: string
}

export interface AuthUser extends User {
  isAuthenticated: boolean
}

// Product/Tutorial types
export interface Tutorial {
  id: string
  title: string
  description: string
  price: number
  featured: boolean
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  duration: number // in seconds
  thumbnail_url: string
  preview_video_url?: string
  videos: Video[]
  created_at?: string
  updated_at?: string
}

export interface Video {
  id: string
  tutorial_id: string
  title: string
  description?: string
  duration: number
  video_url: string
  order_index: number
  created_at?: string
  updated_at?: string
}

// Purchase types
export interface Purchase {
  id: string
  user_id: string
  tutorial_id: string
  amount: number
  paypal_order_id: string
  status: 'pending' | 'completed' | 'failed'
  created_at?: string
  updated_at?: string
}

// Blog types
export interface BlogPost {
  id: string
  title: string
  content: string
  excerpt: string
  featured: boolean
  published: boolean
  author: string
  tags: string[]
  created_at?: string
  updated_at?: string
}

// Portfolio types
export interface PortfolioItem {
  id: string
  title: string
  description: string
  image_url: string
  category: string
  featured: boolean
  date: string
  created_at?: string
  updated_at?: string
}

// Payment types
export interface PaymentIntent {
  id: string
  amount: number
  currency: string
  status: string
  clientSecret?: string
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Form types
export interface LoginForm {
  email: string
  password: string
}

export interface RegisterForm {
  email: string
  password: string
  confirmPassword: string
  name?: string
}

// Database types for SQLite
export interface DbUser {
  id: string
  email: string
  password_hash: string
  name: string | null
  created_at: string
  updated_at: string
}

export interface DbTutorial {
  id: string
  title: string
  description: string
  price: number
  cover_image: string | null
  created_at: string
  updated_at: string
}

export interface DbVideo {
  id: string
  title: string
  description: string | null
  duration: number
  video_url: string
  thumbnail_url: string | null
  order_index: number
  tutorial_id: string
}

export interface DbPurchase {
  id: string
  user_id: string
  tutorial_id: string
  amount: number
  payment_id: string
  status: string
  created_at: string
}
