import { NextRequest, NextResponse } from 'next/server'
import { TutorialModel } from '@/lib/database-supabase'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const tutorial = await TutorialModel.getById(params.id)

    if (!tutorial) {
      return NextResponse.json(
        { success: false, error: 'Tutorial not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: tutorial
    })
  } catch (error: any) {
    console.error('Get tutorial API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to fetch tutorial' 
      },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const updateData = await request.json()
    
    const tutorial = await TutorialModel.update(params.id, updateData)

    if (!tutorial) {
      return NextResponse.json(
        { success: false, error: 'Tutorial not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: tutorial
    })
  } catch (error: any) {
    console.error('Update tutorial API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to update tutorial' 
      },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const success = await TutorialModel.delete(params.id)

    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Tutorial not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Tutorial deleted successfully'
    })
  } catch (error: any) {
    console.error('Delete tutorial API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to delete tutorial' 
      },
      { status: 500 }
    )
  }
}
